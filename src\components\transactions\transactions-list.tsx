"use client";

import { DeleteTransactionDialog } from "@/components/transactions/delete-transaction-dialog";
import { EditTransactionModal } from "@/components/transactions/edit-transaction-modal";
import { TransactionRow } from "@/components/transactions/transaction-row";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Pagination } from "@/components/ui/pagination";
import { cn } from "@/lib/utils";
import { TransactionWithAssetInfo } from "@/utils/db/portfolio-queries";
import { Plus, Receipt } from "lucide-react";
import { useState } from "react";

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  pageSize: number;
  hasMore: boolean;
  hasPrevious: boolean;
}

interface TransactionsListProps {
  transactions: TransactionWithAssetInfo[];
  portfolioId: string;
  portfolioName: string;
  pagination?: PaginationInfo;
  onAddTransaction?: () => void;
  onTransactionUpdated?: () => void;
  onTransactionDeleted?: () => void;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  className?: string;
  isLoading?: boolean;
}

export function TransactionsList({
  transactions,
  portfolioName,
  pagination,
  onAddTransaction,
  onTransactionUpdated,
  onTransactionDeleted,
  onPageChange,
  onPageSizeChange,
  className,
  isLoading = false,
}: TransactionsListProps) {
  const [editingTransaction, setEditingTransaction] =
    useState<TransactionWithAssetInfo | null>(null);
  const [deletingTransaction, setDeletingTransaction] =
    useState<TransactionWithAssetInfo | null>(null);

  const handleEditTransaction = (transaction: TransactionWithAssetInfo) => {
    setEditingTransaction(transaction);
  };

  const handleDeleteTransaction = (transaction: TransactionWithAssetInfo) => {
    setDeletingTransaction(transaction);
  };

  const handleEditModalClose = () => {
    setEditingTransaction(null);
  };

  const handleDeleteDialogClose = () => {
    setDeletingTransaction(null);
  };

  const handleTransactionUpdated = () => {
    setEditingTransaction(null);
    onTransactionUpdated?.();
  };

  const handleTransactionDeleted = () => {
    setDeletingTransaction(null);
    onTransactionDeleted?.();
  };

  if (isLoading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Receipt className="h-6 w-6 text-portavio-orange" />
              <CardTitle className="text-xl">Tranzacții</CardTitle>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-portavio-orange"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (transactions.length === 0) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Receipt className="h-6 w-6 text-portavio-orange" />
              <CardTitle className="text-xl">Tranzacții</CardTitle>
            </div>
            {onAddTransaction && (
              <Button onClick={onAddTransaction} className="gap-2">
                <Plus className="h-4 w-4" />
                Adaugă tranzacție
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="rounded-full bg-muted p-4 mb-4">
              <Receipt className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Nicio tranzacție</h3>
            <p className="text-muted-foreground mb-6 max-w-md">
              Nu există încă tranzacții în portofoliul &quot;{portfolioName}
              &quot;. Adaugă prima ta tranzacție pentru a începe urmărirea
              investițiilor.
            </p>
            {onAddTransaction && (
              <Button onClick={onAddTransaction} className="gap-2">
                <Plus className="h-4 w-4" />
                Adaugă prima tranzacție
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Receipt className="h-6 w-6 text-portavio-orange" />
              <div>
                <CardTitle className="text-xl flex items-center gap-2">
                  Tranzacții
                  <p
                    className="text-lg text-muted-foreground"
                    title="Numărul total de tranzacții (Buy + Sell)"
                  >
                    ({pagination?.totalCount || transactions.length})
                  </p>
                </CardTitle>
              </div>
            </div>
            {onAddTransaction && (
              <Button onClick={onAddTransaction} className="gap-2">
                <Plus className="h-4 w-4" />
                Adaugă tranzacție
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="hidden md:block">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b bg-muted/50">
                    <th className="text-left p-4 font-medium">Activ</th>
                    <th className="text-left p-4 font-medium">Tip</th>
                    <th className="text-right p-4 font-medium">Cantitate</th>
                    <th className="text-right p-4 font-medium">
                      Preț tranzacție
                    </th>
                    <th className="text-right p-4 font-medium">Valoare</th>
                    <th className="text-left p-4 font-medium">Data</th>
                    <th className="text-center p-4 font-medium">Acțiuni</th>
                  </tr>
                </thead>
                <tbody>
                  {transactions.map((transaction) => (
                    <TransactionRow
                      key={transaction.id}
                      transaction={transaction}
                      onEdit={handleEditTransaction}
                      onDelete={handleDeleteTransaction}
                      variant="table"
                    />
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="md:hidden space-y-4 p-4">
            {transactions.map((transaction) => (
              <TransactionRow
                key={transaction.id}
                transaction={transaction}
                onEdit={handleEditTransaction}
                onDelete={handleDeleteTransaction}
                variant="card"
              />
            ))}
          </div>

          {/* Pagination */}
          {pagination &&
            pagination.totalCount > 0 &&
            onPageChange &&
            onPageSizeChange && (
              <div className="border-t p-4">
                <Pagination
                  currentPage={pagination.currentPage}
                  totalPages={pagination.totalPages}
                  pageSize={pagination.pageSize}
                  totalCount={pagination.totalCount}
                  onPageChange={onPageChange}
                  onPageSizeChange={onPageSizeChange}
                />
              </div>
            )}
        </CardContent>
      </Card>

      {editingTransaction && (
        <EditTransactionModal
          open={!!editingTransaction}
          onOpenChange={handleEditModalClose}
          transaction={editingTransaction}
          onSuccess={handleTransactionUpdated}
        />
      )}

      {deletingTransaction && (
        <DeleteTransactionDialog
          open={!!deletingTransaction}
          onOpenChange={handleDeleteDialogClose}
          transaction={deletingTransaction}
          onSuccess={handleTransactionDeleted}
        />
      )}
    </>
  );
}
