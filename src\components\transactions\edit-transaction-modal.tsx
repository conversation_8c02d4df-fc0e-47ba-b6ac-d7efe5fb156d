"use client";

import { TickerSearch } from "@/components/transactions/ticker-search";
import { But<PERSON> } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker-simple";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useUpdateTransaction } from "@/hooks/use-transactions-query";
import {
  EditTransactionFormData,
  editTransactionFormSchema,
  getChangedTransactionFields,
  getEditTransactionFormData,
  hasTransactionChanged,
} from "@/lib/transaction-schemas";
import { TransactionWithAssetInfo } from "@/utils/db/portfolio-queries";
import { zodResolver } from "@hookform/resolvers/zod";
import { format, parseISO } from "date-fns";
import { Edit, Loader2, Minus, Plus, XCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { toast } from "sonner";

interface EditTransactionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  transaction: TransactionWithAssetInfo;
  onSuccess?: () => void;
}

export function EditTransactionModal({
  open,
  onOpenChange,
  transaction,
  onSuccess,
}: EditTransactionModalProps) {
  const [portfolioAssets, setPortfolioAssets] = useState<any[]>([]);
  const [portfolioAssetsLoading, setPortfolioAssetsLoading] = useState(false);
  const [portfolioAssetsError, setPortfolioAssetsError] = useState<
    string | null
  >(null);

  // Use TanStack Query mutation for updating transactions
  const updateTransactionMutation = useUpdateTransaction();

  const form = useForm<EditTransactionFormData>({
    resolver: zodResolver(editTransactionFormSchema),
    defaultValues: getEditTransactionFormData(transaction),
  });

  const watchedTransactionType = useWatch({
    control: form.control,
    name: "type",
  });

  useEffect(() => {
    if (watchedTransactionType === "SELL" && transaction.portfolio_id) {
      const fetchPortfolioAssets = async () => {
        try {
          setPortfolioAssetsLoading(true);
          setPortfolioAssetsError(null);

          const tickersResponse = await fetch(
            `/api/portfolios/${transaction.portfolio_id}/tickers`
          );
          if (!tickersResponse.ok) {
            throw new Error("Nu s-au putut încărca activele din portofoliu");
          }
          const tickersData = await tickersResponse.json();
          const tickers = tickersData.tickers || [];

          const assets = tickers.map((ticker: string) => ({
            ticker,
            name: ticker,
            description: ticker,
            source: "database",
            isExistingAsset: true,
          }));

          setPortfolioAssets(assets);
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : "A apărut o eroare";
          setPortfolioAssetsError(errorMessage);
          console.error("Error fetching portfolio assets:", error);
        } finally {
          setPortfolioAssetsLoading(false);
        }
      };

      fetchPortfolioAssets();
    } else {
      setPortfolioAssets([]);
      setPortfolioAssetsError(null);
    }
  }, [watchedTransactionType, transaction.portfolio_id]);

  const handleSubmit = async (data: EditTransactionFormData) => {
    if (!hasTransactionChanged(transaction, data)) {
      toast.info("Nu au fost detectate modificări");
      onOpenChange(false);
      return;
    }

    const changes = getChangedTransactionFields(transaction, data);
    console.log("Edit Transaction Modal - Form data:", data);
    console.log("Edit Transaction Modal - Original transaction:", transaction);
    console.log("Edit Transaction Modal - Changes to send:", changes);

    updateTransactionMutation.mutate(
      {
        transactionId: transaction.id,
        data: changes,
      },
      {
        onSuccess: () => {
          toast.success("Tranzacția a fost actualizată cu succes!");
          onOpenChange(false);
          onSuccess?.();
        },
        onError: (error) => {
          const errorMessage =
            error instanceof Error ? error.message : "A apărut o eroare";
          toast.error(errorMessage);
        },
      }
    );
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && !updateTransactionMutation.isPending) {
      form.reset();
    }
    onOpenChange(newOpen);
  };

  const handleTransactionTypeChange = (value: string) => {
    form.setValue("type", value as EditTransactionFormData["type"]);
    form.setValue("ticker", "");
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold flex items-center gap-2">
            <Edit className="h-6 w-6 text-portavio-orange" />
            Editează Tranzacția
          </DialogTitle>
          <DialogDescription>
            Modifică detaliile tranzacției pentru {transaction.ticker}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipul tranzacției</FormLabel>
                  <Select
                    onValueChange={handleTransactionTypeChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selectează tipul tranzacției" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="BUY">
                        <div className="flex items-center gap-2">
                          <Plus className="h-4 w-4 text-green-600" />
                          <span>BUY</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="SELL">
                        <div className="flex items-center gap-2">
                          <Minus className="h-4 w-4 text-red-600" />
                          <span>SELL</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Ticker */}
            <FormField
              control={form.control}
              name="ticker"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Simbolul activului</FormLabel>
                  <FormControl>
                    <TickerSearch
                      value={field.value}
                      onValueChange={field.onChange}
                      placeholder="Caută sau introdu simbolul (ex: AAPL, MSFT)"
                      disabled={portfolioAssetsLoading}
                      error={!!portfolioAssetsError}
                      assets={
                        watchedTransactionType === "SELL"
                          ? portfolioAssets
                          : undefined
                      }
                      allowAssetCreation={watchedTransactionType !== "SELL"}
                      allowCustomValue={watchedTransactionType !== "SELL"}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Transaction Date */}
            <FormField
              control={form.control}
              name="transactionDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data tranzacției</FormLabel>
                  <FormControl>
                    <DatePicker
                      value={field.value ? parseISO(field.value) : undefined}
                      onChange={(date) => {
                        if (date) {
                          field.onChange(format(date, "yyyy-MM-dd"));
                        } else {
                          field.onChange(undefined);
                        }
                      }}
                      placeholder="Selectează data tranzacției"
                      maxDate={(() => {
                        const yesterday = new Date();
                        yesterday.setDate(yesterday.getDate() - 1);
                        return yesterday;
                      })()}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Price */}
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Preț per unitate</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.00000001"
                      min="0"
                      max="999999999"
                      placeholder="ex. 150.25 sau 0.001"
                      {...field}
                      onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(
                          value === "" ? undefined : parseFloat(value)
                        );
                      }}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Transaction Fee (Optional) */}
            <FormField
              control={form.control}
              name="transaction_fee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Comision tranzacție (Opțional)</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type="number"
                        placeholder="0,00"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(
                            value === "" ? undefined : parseFloat(value)
                          );
                        }}
                        value={field.value || ""}
                      />
                      <Button
                        onClick={() => field.onChange(0)}
                        variant={"ghost"}
                        className="absolute right-10 top-1/2 -translate-y-1/2"
                        title="Șterge comisionul"
                        type="button"
                      >
                        <XCircle className="h-4 w-4" />
                      </Button>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Comisionul plătit pentru această tranzacție
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Quantity */}
            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cantitatea</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.00000001"
                      min="0"
                      max="999999999"
                      placeholder="ex. 10 sau 10.5"
                      {...field}
                      onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(
                          value === "" ? undefined : parseFloat(value)
                        );
                      }}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Note (opțional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Adaugă note despre această tranzacție..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submit Buttons */}
            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={updateTransactionMutation.isPending}
              >
                Anulează
              </Button>
              <Button
                type="submit"
                disabled={updateTransactionMutation.isPending}
              >
                {updateTransactionMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Se actualizează...
                  </>
                ) : (
                  "Actualizează tranzacția"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
