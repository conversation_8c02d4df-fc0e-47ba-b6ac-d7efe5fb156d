"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { EmptyPortfoliosState } from "@/components/portfolios/empty-portfolios-state";
import { PortfolioErrorState } from "@/components/portfolios/portfolio-error-state";
import { PortfolioGrid } from "@/components/portfolios/portfolio-grid";
import { PortfolioGridSkeleton } from "@/components/portfolios/portfolio-skeleton";
import { AddTransactionModal } from "@/components/transactions/add-transaction-modal";
import { PortfolioWithMetrics } from "@/utils/db/portfolio-queries";
import { Plus, PieChart, FolderPlus } from "lucide-react";
import { useEffect, useState } from "react";
import Link from "next/link";

interface PortfoliosClientProps {
  className?: string;
}

export function PortfoliosClient({ className }: PortfoliosClientProps) {
  const [portfolios, setPortfolios] = useState<PortfolioWithMetrics[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPortfolioId, setSelectedPortfolioId] = useState<
    string | undefined
  >();

  const loadPortfolios = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch("/api/portfolios/metrics", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Nu s-au putut încărca portofoliile"
        );
      }

      const data = await response.json();
      setPortfolios(data.portfolios || []);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "A apărut o eroare";
      setError(errorMessage);
      console.error("Error loading portfolios:", err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadPortfolios();
  }, []);

  const handleRetry = () => {
    loadPortfolios();
  };

  const handleModalSuccess = () => {
    loadPortfolios();
  };

  const handleAddTransaction = (portfolioId?: string) => {
    setSelectedPortfolioId(portfolioId);
    setIsModalOpen(true);
  };

  const handleModalOpenChange = (open: boolean) => {
    setIsModalOpen(open);
    if (!open) {
      setSelectedPortfolioId(undefined);
    }
  };

  if (isLoading) {
    return (
      <div className={className}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-8 w-48 bg-muted animate-pulse rounded" />
              <div className="h-4 w-96 bg-muted animate-pulse rounded" />
            </div>
            <div className="h-10 w-32 bg-muted animate-pulse rounded" />
          </div>

          <PortfolioGridSkeleton count={6} />
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={className}>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between flex-wrap gap-2">
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                Portofoliile mele
              </h1>
              <p className="text-muted-foreground mt-2">
                Urmărește-ți investițiile și performanța portofoliilor
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" className="gap-2" asChild>
                <Link href="/portfolios/create">
                  <FolderPlus className="h-4 w-4" />
                  Creează Portofoliu
                </Link>
              </Button>
            </div>
          </div>

          <PortfolioErrorState error={error} onRetry={handleRetry} />
        </div>
      </div>
    );
  }

  // Empty state
  if (portfolios.length === 0) {
    return (
      <div className={className}>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between flex-wrap gap-2">
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                Portofoliile mele
              </h1>
              <p className="text-muted-foreground mt-2">
                Urmărește-ți investițiile și performanța portofoliilor
              </p>
            </div>
            <div className="flex items-center gap-3 flex-wrap">
              <Button variant="outline" className="gap-2" asChild>
                <Link href="/portfolios/create">
                  <FolderPlus className="h-4 w-4" />
                  Creează Portofoliu
                </Link>
              </Button>
              <Button className="gap-2" onClick={() => handleAddTransaction()}>
                <Plus className="h-4 w-4" />
                Adaugă tranzacție
              </Button>
            </div>
          </div>

          <AddTransactionModal
            open={isModalOpen}
            onOpenChange={handleModalOpenChange}
            onSuccess={handleModalSuccess}
            defaultPortfolioId={selectedPortfolioId}
          />
          <EmptyPortfoliosState
            onAddTransaction={() => handleAddTransaction()}
          />
        </div>
      </div>
    );
  }

  // Success state with portfolios
  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between flex-wrap gap-2">
          <div>
            <div className="flex items-center gap-3">
              <PieChart className="h-8 w-8 text-portavio-blue" />
              <h1 className="text-3xl font-bold text-foreground">
                Portofoliile mele
              </h1>
            </div>
            <p className="text-muted-foreground mt-2">
              Urmărește-ți investițiile și performanța portofoliilor
            </p>
          </div>
          <div className="flex items-center gap-3 flex-wrap">
            <Button variant="outline" className="gap-2" asChild>
              <Link href="/portfolios/create">
                <FolderPlus className="h-4 w-4" />
                Creează Portofoliu
              </Link>
            </Button>
            <Button className="gap-2" onClick={() => handleAddTransaction()}>
              <Plus className="h-4 w-4" />
              Adaugă tranzacție
            </Button>
          </div>
        </div>

        {/* Portfolio Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-card border rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-md bg-portavio-blue/10">
                <PieChart className="h-5 w-5 text-portavio-blue" />
              </div>
              <div>
                <p className="text-2xl font-bold">{portfolios.length}</p>
                <p className="text-sm text-muted-foreground">
                  {portfolios.length === 1
                    ? "Portofoliu activ"
                    : "Portofolii active"}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-card border rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-md bg-portavio-orange/10">
                <Plus className="h-5 w-5 text-portavio-orange" />
              </div>
              <div>
                <p className="text-2xl font-bold">
                  {portfolios.reduce(
                    (sum, p) => sum + p.metrics.totalHoldings,
                    0
                  )}
                </p>
                <p className="text-sm text-muted-foreground">Total poziții</p>
              </div>
            </div>
          </div>

          <div className="bg-card border rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-md bg-green-500/10">
                <Plus className="h-5 w-5 text-green-500" />
              </div>
              <div>
                <p className="text-2xl font-bold">
                  {portfolios.reduce(
                    (sum, p) => sum + p.metrics.totalTransactions,
                    0
                  )}
                </p>
                <p className="text-sm text-muted-foreground">
                  Total tranzacții
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Portfolio Grid */}
        <PortfolioGrid
          portfolios={portfolios}
          onAddTransaction={handleAddTransaction}
        />
      </div>

      <AddTransactionModal
        open={isModalOpen}
        onOpenChange={handleModalOpenChange}
        onSuccess={handleModalSuccess}
        defaultPortfolioId={selectedPortfolioId}
      />
    </div>
  );
}
